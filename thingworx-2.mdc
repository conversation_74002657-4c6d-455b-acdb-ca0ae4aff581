---
globs: twx/**/**.js
alwaysApply: false
---
# ThingWorx 服务开发规范

## 1. 核心开发准则

### 1.1. ES5 语法约束
ThingWorx 服务脚本严格使用 **ES5** 语法。严禁使用包括但不限于以下 ES6+ 特性：
- `let` / `const` (使用 `var`)
- 箭头函数 `=>` (使用 `function`)
- 模板字符串 `` `${...}` `` (使用 `+` 拼接)
- 解构赋值
- 展开运算符 `...`
- `Promise`, `async/await`
- `class`

### 1.2. 结果返回机制
- **禁止使用 `return`**：在服务脚本中，不允许使用 `return` 语句提前退出。若需中断，应使用 `throw` 抛出异常。
- **`result` 变量**：所有服务的最终输出都必须赋值给名为 `result` 的全局变量。

### 1.3. 命名规范
- **服务 (Service)**: `PascalCase` (例: `QueryUsers`, `CreateOrder`)
- **变量 (Variable)**: `camelCase` (例: `userName`, `pageNumber`)
- **数据库相关**: 表名和字段名遵循数据库的实际大小写（Oracle 通常为大写）。

### 1.4. 日志规范
- **日志级别**: 合理使用 `logger.debug()`, `logger.info()`, `logger.warn()`, `logger.error()`。
- **内容格式**: 日志信息必须包含服务名作为前缀，便于追踪。
  ```javascript
  // 正例
  logger.error("MyService - 操作失败，原因: " + error);
  ```
- **日志限流**: 避免在循环中高频记录日志，应在关键节点或聚合后输出。

## 2. 服务结构与文档

### 2.1. JSDoc 注释规范
每个服务都必须包含完整的 JSDoc 注释，用于说明其功能、参数和返回值。

```javascript
/**
 * @definition    [服务名称]    {"category":"ext"}
 * @description   [服务功能描述]  [作者] [创建/修改日期时间 YYYY年MM月DD日HH:mm:ss]
 * @implementation    {Script}
 *
 * @param    {[类型]}    [参数名]    [参数描述]
 * @param    {[类型]}    [参数名]    [参数描述]
 *
 * @returns    {[返回类型]}
 */
```

### 2.2. 服务代码结构
推荐采用模块化的思想，将复杂逻辑拆分为多个内部服务，并通过 `me.serviceName()` 的方式调用。

```javascript
// 推荐结构
// 1. 变量初始化
var res = { success: false, msg: "" };

try {
    // 2. 参数校验
    if (!param) {
        throw "必要参数不能为空";
    }

    // 3. 调用内部服务或执行核心逻辑
    var internalData = me.getInternalData({ id: param });

    // 4. 处理业务逻辑
    // ...

    // 5. 设置成功响应
    res.success = true;
    res.data = processedData;
    res.msg = "操作成功";

} catch (error) {
    // 6. 异常处理与日志记录
    var msg = "ServiceName - 操作失败: " + error;
    logger.error(msg);
    res.success = false;
    res.msg = msg; // 返回给调用方的信息
}

// 7. 最终结果赋值
result = res;
```

## 3. 返回值与错误处理

### 3.1. 标准响应模式
根据服务类型，选择统一的返回值结构。

- **简单查询**: 直接返回 `InfoTable` 或数组。
  ```javascript
  result = Things['Thing.DB.Oracle'].RunQuery({sql: sql});
  ```
- **操作类服务 (增/删/改)**: 返回标准 JSON 对象，指明操作状态。
  ```javascript
  var res = {
      success: true, // 或 false
      msg: "操作成功", // 或 错误信息
      data: {} // 可选，附加数据
  };
  result = res;
  ```
- **分页查询**: 返回 LayUI 兼容的 JSON 对象。
  ```javascript
  var res = {
      code: 0, // 0表示成功
      msg: "",
      count: 0, // 总记录数
      data: [] // 当前页数据 (InfoTable或Array)
  };
  result = res;
  ```

### 3.2. 错误处理
- **必须使用 `try-catch`**: 所有可能产生异常的复杂服务（特别是涉及 I/O 操作）都必须包裹在 `try-catch` 块中。
- **抛出异常**: 使用 `throw "错误信息"` 来中断执行流程。
- **日志记录**: 在 `catch` 块中，必须使用 `logger.error()` 记录详细的错误信息。
- **友好提示**: 返回给前端的 `msg` 字段应是用户可理解的提示，避免暴露系统内部细节。

## 4. 数据库操作 (Oracle)

### 4.1. 基本操作
- **查询**: `Things['Thing.DB.Oracle'].RunQuery({sql: sql})`
- **更新/插入/删除**: `Things['Thing.DB.Oracle'].RunCommand({sql: sql})`

### 4.2. SQL 安全与注入防护
- **严禁拼接原始输入**: 绝对禁止将未经处理的用户输入直接拼接到 SQL 语句中。
- **输入转义**: 对所有字符串类型的输入参数，在拼接到 SQL 前必须进行转义。
  ```javascript
  function sqlEscape(v) {
      if (v === null || v === undefined) { return ""; }
      return String(v).replace(/'/g, "''");
  }
  var sql = "select * from users where name = '" + sqlEscape(userName) + "'";
  ```
- **白名单校验**: 对用于 `order by` 或动态表/字段名的输入，必须进行白名单校验。
  ```javascript
  var allowedOrderFields = { "id": true, "create_time": true };
  var orderBy = allowedOrderFields[orderField] ? orderField : "id"; // 兜底默认值
  ```
- **安全构建 `IN` 子句**:
  ```javascript
  function buildInList(arr) {
      var list = [];
      for (var i = 0; i < arr.length; i++) {
          list.push("'" + sqlEscape(arr[i]) + "'");
      }
      return list.length ? "(" + list.join(",") + ")" : "(NULL)";
  }
  ```

### 4.3. 性能优化
- **避免 N+1 查询**: 严禁在循环中执行数据库查询。应先一次性查出所有相关数据，然后在内存中处理。
- **批量操作**: 对于批量插入/更新，优先使用 `MERGE` 语句，而不是在循环中执行 `RunCommand`。
  ```javascript
  // MERGE 语句模板
  var subQuery = "select 'A' id, '1' val from dual union all select 'B','2' from dual"; // 动态构建
  var mergeSql = "merge into target_table t using (" + subQuery + ") s on (t.id = s.id) " +
                 "when matched then update set t.val = s.val " +
                 "when not matched then insert (id, val) values (s.id, s.val)";
  Things['Thing.DB.Oracle'].RunCommand({ sql: mergeSql });
  ```
- **分页查询**: 使用 `ROWNUM` 进行分页，并为 `pageSize` 设置合理的上限（如 1000）。
  ```javascript
  var startRowno = (pageNumber - 1) * pageSize + 1;
  var endRowno = pageNumber * pageSize;
  var sql = "select * from (select ROWNUM as rowno, a.* from (" +
            "select * from table_name " + whereClause + " order by " + orderBy + " desc" +
            ") a) s where s.rowno >= " + startRowno + " and s.rowno <= " + endRowno;
  ```

### 4.4. 幂等性设计
对于创建或更新操作，应设计为幂等的。先查询记录是否存在，再决定执行插入还是更新。`MERGE` 语句是实现幂等性的最佳方式。

## 5. 外部 API 调用

统一使用 `Resources["ContentLoaderFunctions"]` 进行 HTTP 调用。

- **GET 请求**:
  ```javascript
  var options = {
      url: apiUrl,
      headers: { "Accept": "application/json" },
      timeout: 10000 // 建议设置超时时间
  };
  var resp = Resources["ContentLoaderFunctions"].GetJSON(options);
  ```
- **POST 请求**:
  ```javascript
  var options = {
      url: apiUrl,
      content: JSON.stringify(body),
      headers: { "Content-Type": "application/json" },
      timeout: 10000
  };
  var resp = Resources["ContentLoaderFunctions"].PostJSON(options);
  ```
- **规范要点**:
  - API 调用必须置于 `try-catch` 块内。
  - `url` 和敏感凭证应从配置（如 Thing 属性）中读取，禁止硬编码。
  - 必须对返回值 `resp` 进行判空和类型校验，防止后续代码出错。
  - 对外暴露的错误信息应保持友好，详细错误写入日志。

## 6. 数据处理

### 6.1. InfoTable 操作
- **创建**: 使用 `DataShapes["ShapeName"].CreateValues()` 或 `Resources["InfoTableFunctions"].CreateInfoTableFromDataShape()`。
- **遍历**: 使用标准 `for` 循环遍历 `infotable.rows`。
- **空值处理**: 从数据库查询出的 InfoTable，建议遍历处理 `null` 或 `undefined` 的字段，统一转换为空字符串 `""`，保证前端健壮性。

### 6.2. 条件 SQL 构建
动态构建 `where` 子句时，注意 `and` 的拼接逻辑。

```javascript
var conditionSql = "";
if (condition1) {
    conditionSql += " and field1 = '" + sqlEscape(condition1) + "'";
}
if (condition2) {
    conditionSql += " and field2 like '%" + sqlEscape(condition2) + "%'";
}
// 在主SQL中: "select * from table where 1=1" + conditionSql
```

## 7. 安全与并发

### 7.1. 权限控制
- **服务权限**: 在 Composer 中为服务设置最小化的可见性 (Visibility) 和权限 (Permissions)。
- **数据权限**: 服务逻辑必须校验当前用户是否有权访问或操作目标数据。
- **凭证管理**: 密码、Token 等敏感信息必须存储在 `PASSWORD` 类型的 Thing 属性中，严禁硬编码或记入日志。

### 7.2. 并发控制
- **长时任务**: 超过30秒的服务应拆分为异步任务（如使用 Scheduler），前端通过轮询查询结果。
- **防止重复提交**: 对关键操作可引入“运行锁”机制（如通过数据库表或 Thing 属性），在服务开始时检查，结束后释放。
  ```javascript
  // 简易锁逻辑
  // try-finally 确保锁一定被释放
  try {
      // 加锁
      me.SetProperty("isJobRunning", true);
      // ... 业务逻辑 ...
  } finally {
      // 解锁
