# 实验鉴定数据看板

## 项目说明
本项目是一个用于展示实验鉴定数据的可视化大屏系统。系统展示了产品状态统计、产品数量统计以及文件类别统计等数据。

## 功能模块

### 1. 型号筛选
- 支持按型号筛选数据
- 默认显示所有型号数据

### 2. 产品状态统计
- 以表格形式展示各状态下的产品数量
- 包含10个状态:大纲编制、大纲审查、报告编制、报告审查、小批量报告编制、小批量报告审查、状态鉴定文件编制、院内审查、前置审查、正式审查
- 按型号和分系统(一级、二级、三级、四级)统计
- 表头之间使用jsPlumb连接展示

### 3. 产品数量统计
- 使用饼图展示各分系统下的产品数量
- 与型号选择联动

### 4. 文件类别统计 
- 使用饼图展示不同文件类别的数量分布
- 与型号选择联动

## 部署说明

### 导出部署包 (export_package.bat)
1. 位置：`deploy/export_package.bat`
2. 功能：导出指定日期之后修改的文件
3. 使用方法：
   - 双击运行脚本
   - 输入上次导出日期（格式：YYYY-MM-DD）
   - 等待导出完成
4. 输出：
   - 导出文件保存在：`deploy/package/` 目录
   - 导出日志保存在：`deploy/export_log.txt`

### 自动部署 (deploy_package.bat)
1. 位置：`deploy/deploy_package.bat`
2. 功能：将部署包文件更新到目标目录
3. 使用方法：
   - 确保已运行 export_package.bat
   - 双击运行脚本
   - 等待部署完成
4. 特点：
   - 自动备份原有文件
   - 保持原有目录结构
   - 详细的部署日志
5. 输出：
   - 备份文件保存在：`deploy/backup/日期/` 目录
   - 部署日志保存在：`deploy/deploy_log.txt`

## 技术栈
- jQuery 
- Layui (最新版)
- ECharts
- jsPlumb
- ES5语法

## 目录结构
```
DataPackageManagement/
  ├── components/                # 前端组件
  │   └── testEvaluation/       # 实验鉴定数据看板
  ├── deploy/                   # 部署相关脚本
  │   ├── export_package.bat    # 导出部署包脚本
  │   ├── deploy_package.bat    # 自动部署脚本
  │   ├── package/             # 导出的部署包
  │   └── backup/              # 备份文件
  ├── twx/                      # 后端服务
  │   └── services/
  │       └── Thing.Fn.TestEvaluation/  # 后端API服务
  └── README.md                 # 项目说明文档
```
